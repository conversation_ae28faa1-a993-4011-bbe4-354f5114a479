#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.seams.models import Role, Hardware, Software, Certificate, IPAddress, User

def populate_sample_data():
    """
    Populate the SEAMS database with sample data
    """
    print("Populating SEAMS database with sample data...")
    
    # Create Roles
    roles_data = [
        {'role': 'Administrator'},
        {'role': 'IT Manager'},
        {'role': 'Security Officer'},
        {'role': 'End User'},
        {'role': 'Contractor'},
    ]
    
    for role_data in roles_data:
        role, created = Role.objects.get_or_create(**role_data)
        if created:
            print(f"Created role: {role.role}")
    
    # Create Hardware
    hardware_data = [
        {
            'model': 'Dell OptiPlex 7090',
            'serial': 'DL001234',
            'location': 'Office Floor 1',
            'user_assigned': '<PERSON>',
            'install_date': '2024-01-15'
        },
        {
            'model': 'HP EliteBook 850',
            'serial': 'HP567890',
            'location': 'Office Floor 2',
            'user_assigned': '<PERSON>',
            'install_date': '2024-02-20'
        },
        {
            'model': 'Cisco Catalyst 2960',
            'serial': 'CS789012',
            'location': 'Server Room',
            'user_assigned': None,
            'install_date': '2023-12-01'
        },
        {
            'model': 'MacBook Pro M3',
            'serial': 'MB345678',
            'location': 'Office Floor 3',
            'user_assigned': 'Alice Johnson',
            'install_date': '2024-03-10'
        },
    ]
    
    for hw_data in hardware_data:
        hardware, created = Hardware.objects.get_or_create(
            serial=hw_data['serial'],
            defaults=hw_data
        )
        if created:
            print(f"Created hardware: {hardware.model}")
    
    # Create Software
    software_data = [
        {
            'name': 'Microsoft Office 365',
            'serial': 'MS365-001',
            'user_assigned': 'John Doe',
            'hardware_assigned': Hardware.objects.filter(serial='DL001234').first(),
            'install_date': '2024-01-16',
            'expiry_date': '2025-01-16'
        },
        {
            'name': 'Adobe Creative Suite',
            'serial': 'ACS-002',
            'user_assigned': 'Jane Smith',
            'hardware_assigned': Hardware.objects.filter(serial='HP567890').first(),
            'install_date': '2024-02-21',
            'expiry_date': '2025-02-21'
        },
        {
            'name': 'Antivirus Enterprise',
            'serial': 'AV-003',
            'user_assigned': None,
            'hardware_assigned': Hardware.objects.filter(serial='CS789012').first(),
            'install_date': '2023-12-02',
            'expiry_date': '2024-12-02'
        },
    ]
    
    for sw_data in software_data:
        software, created = Software.objects.get_or_create(
            serial=sw_data['serial'],
            defaults=sw_data
        )
        if created:
            print(f"Created software: {software.name}")
    
    # Create Certificates
    cert_data = [
        {
            'certificate_type': 'SSL Certificate',
            'serial': 'SSL-001',
            'hardware_assigned': Hardware.objects.filter(serial='CS789012').first(),
            'install_date': '2024-01-01',
            'expiry_date': '2025-01-01'
        },
        {
            'certificate_type': 'Code Signing',
            'serial': 'CS-002',
            'hardware_assigned': Hardware.objects.filter(serial='DL001234').first(),
            'install_date': '2024-02-01',
            'expiry_date': '2025-02-01'
        },
    ]
    
    for cert in cert_data:
        certificate, created = Certificate.objects.get_or_create(
            serial=cert['serial'],
            defaults=cert
        )
        if created:
            print(f"Created certificate: {certificate.certificate_type}")
    
    # Create IP Addresses
    ip_data = [
        {
            'ip_type': 'Static',
            'address': '***********00',
            'hardware_assigned': Hardware.objects.filter(serial='DL001234').first(),
            'install_date': '2024-01-15',
            'expiry_date': None
        },
        {
            'ip_type': 'DHCP',
            'address': '***********50',
            'hardware_assigned': Hardware.objects.filter(serial='HP567890').first(),
            'install_date': '2024-02-20',
            'expiry_date': None
        },
        {
            'ip_type': 'Static',
            'address': '***********',
            'hardware_assigned': Hardware.objects.filter(serial='CS789012').first(),
            'install_date': '2023-12-01',
            'expiry_date': None
        },
    ]
    
    for ip in ip_data:
        ip_address, created = IPAddress.objects.get_or_create(
            address=ip['address'],
            defaults=ip
        )
        if created:
            print(f"Created IP address: {ip_address.address}")
    
    # Create Users
    admin_role = Role.objects.filter(role='Administrator').first()
    it_role = Role.objects.filter(role='IT Manager').first()
    user_role = Role.objects.filter(role='End User').first()
    
    users_data = [
        {
            'first_name': 'John',
            'last_name': 'Doe',
            'email_address': '<EMAIL>',
            'role': user_role,
            'department': 'Finance',
            'mfa_status': True
        },
        {
            'first_name': 'Jane',
            'last_name': 'Smith',
            'email_address': '<EMAIL>',
            'role': it_role,
            'department': 'IT',
            'mfa_status': True
        },
        {
            'first_name': 'Alice',
            'last_name': 'Johnson',
            'email_address': '<EMAIL>',
            'role': admin_role,
            'department': 'Administration',
            'mfa_status': True
        },
        {
            'first_name': 'Bob',
            'last_name': 'Wilson',
            'email_address': '<EMAIL>',
            'role': user_role,
            'department': 'Marketing',
            'mfa_status': False
        },
    ]
    
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            email_address=user_data['email_address'],
            defaults=user_data
        )
        if created:
            print(f"Created user: {user.first_name} {user.last_name}")
    
    print("\nSample data population completed!")
    print(f"Created {Role.objects.count()} roles")
    print(f"Created {Hardware.objects.count()} hardware items")
    print(f"Created {Software.objects.count()} software items")
    print(f"Created {Certificate.objects.count()} certificates")
    print(f"Created {IPAddress.objects.count()} IP addresses")
    print(f"Created {User.objects.count()} users")

if __name__ == '__main__':
    populate_sample_data()
