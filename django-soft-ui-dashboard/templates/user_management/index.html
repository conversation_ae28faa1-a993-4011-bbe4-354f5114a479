{% extends "layouts/base.html" %}
{% load static %}

{% block title %} {% if page_title %} page_title {% else %} User Management {% endif %} {% endblock title %}

{% block content %}

<div class="container-fluid py-4">
    <div class="row align-items-center">
        <div class="col-md-12">
            <ul class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'user_management' %}">User Management</a>
                </li>
            </ul>
        </div>

        <div class="col-sm-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h5>
                        User Management
                    </h5>
                </div>
                <div class="card-body pt-2">
                    <ul>
                        {% for link in routes %}
                            <li>
                                <a href="{% url "user_model_dt" link %}">{{ link }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
