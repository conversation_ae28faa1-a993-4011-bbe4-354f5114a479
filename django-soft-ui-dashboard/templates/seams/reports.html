{% extends "layouts/base.html" %}

{% block title %} SEAMS Reports {% endblock title %}

{% block breadcrumbs %}{% endblock breadcrumbs %}

{% block content %}

<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header pb-0">
          <h6>SEAMS Reports</h6>
          <p class="text-sm mb-0">
            Asset management reports and analytics for hardware, software, certificates, and IP addresses.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Hardware by Location Report -->
  <div class="row">
    <div class="col-lg-6 mb-4">
      <div class="card h-100">
        <div class="card-header pb-0">
          <h6>Hardware by Location</h6>
        </div>
        <div class="card-body">
          {% if hardware_by_location %}
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Location</th>
                    <th>Count</th>
                  </tr>
                </thead>
                <tbody>
                  {% for location, count in hardware_by_location.items %}
                  <tr>
                    <td>{{ location }}</td>
                    <td><span class="badge bg-primary">{{ count }}</span></td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <p class="text-muted">No hardware data available.</p>
          {% endif %}
        </div>
      </div>
    </div>
    
    <div class="col-lg-6 mb-4">
      <div class="card h-100">
        <div class="card-header pb-0">
          <h6>Software Expiring Soon</h6>
        </div>
        <div class="card-body">
          {% if expiring_software %}
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Software</th>
                    <th>Expiry Date</th>
                    <th>User</th>
                  </tr>
                </thead>
                <tbody>
                  {% for software in expiring_software %}
                  <tr>
                    <td>{{ software.name }}</td>
                    <td><span class="badge bg-warning">{{ software.expiry_date|default:"No Date" }}</span></td>
                    <td>{{ software.user_assigned|default:"Unassigned" }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <p class="text-muted">No expiring software found.</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Links to Data Tables -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header pb-0">
          <h6>Detailed Asset Management</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/hardware/" class="btn btn-outline-primary w-100">
                <i class="ni ni-laptop me-2"></i>Hardware Details
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/software/" class="btn btn-outline-info w-100">
                <i class="ni ni-app me-2"></i>Software Details
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/certificates/" class="btn btn-outline-warning w-100">
                <i class="ni ni-lock-circle-open me-2"></i>Certificate Details
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/ipaddress/" class="btn btn-outline-success w-100">
                <i class="ni ni-world me-2"></i>IP Address Details
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/users/" class="btn btn-outline-primary w-100">
                <i class="ni ni-single-02 me-2"></i>User Details
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/roles/" class="btn btn-outline-secondary w-100">
                <i class="ni ni-badge me-2"></i>Role Details
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Back to Dashboard -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body text-center">
          <a href="{% url "seams_dashboard" %}" class="btn btn-primary">
            <i class="ni ni-curved-next me-2"></i>Back to SEAMS Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock content %}
