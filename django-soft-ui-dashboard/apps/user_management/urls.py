from django.urls import path
from apps.user_management import views

urlpatterns = [
    path('user-management/', views.index, name="user_management"),

    path('user-create-filter/<str:model_name>/', views.create_filter, name="user_create_filter"),
    path('user-create-page-items/<str:model_name>/', views.create_page_items, name="user_create_page_items"),
    path('user-create-hide-show-items/<str:model_name>/', views.create_hide_show_filter, name="user_create_hide_show_filter"),
    path('user-delete-filter/<str:model_name>/<int:id>/', views.delete_filter, name="user_delete_filter"),
    path('user-create/<str:aPath>/', views.create, name="user_create"),
    path('user-delete/<str:aPath>/<int:id>/', views.delete, name="user_delete"),
    path('user-update/<str:aPath>/<int:id>/', views.update, name="user_update"),

    path('user-export-csv/<str:aPath>/', views.ExportCSVView.as_view(), name='user_export_csv'),

    path('user-management/<str:aPath>/', views.model_dt, name="user_model_dt"),
]
