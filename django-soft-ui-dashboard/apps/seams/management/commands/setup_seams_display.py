from django.core.management.base import BaseCommand
from django.conf import settings
from apps.dyn_dt.models import HideShowFilter
from apps.seams.models import Hardware, Software, Certificate, IPAddress, User, Role


class Command(BaseCommand):
    help = 'Set up default field visibility for SEAMS models in dynamic datatables'

    def handle(self, *args, **options):
        """
        Set up default hide/show filters for SEAMS models to hide primary key fields
        """
        
        # SEAMS model mappings from settings
        seams_models = {
            'hardware': Hardware,
            'software': Software,
            'certificates': Certificate,
            'ipaddress': IPAddress,
            'users': User,
            'roles': Role,
        }
        
        for model_slug, model_class in seams_models.items():
            self.stdout.write(f"Setting up display fields for {model_slug}...")
            
            # Get all fields for this model
            all_fields = [field.name for field in model_class._meta.fields]
            pk_field = model_class._meta.pk.name
            
            for field_name in all_fields:
                # Hide primary key fields by default, show all others
                should_hide = (field_name == pk_field)
                
                hide_show_filter, created = HideShowFilter.objects.get_or_create(
                    parent=model_slug,
                    key=field_name,
                    defaults={'value': should_hide}
                )
                
                if created:
                    status = "hidden" if should_hide else "visible"
                    self.stdout.write(f"  - {field_name}: {status} (created)")
                else:
                    # Update existing filter if it's for a primary key
                    if field_name == pk_field and not hide_show_filter.value:
                        hide_show_filter.value = True
                        hide_show_filter.save()
                        self.stdout.write(f"  - {field_name}: hidden (updated)")
                    else:
                        status = "hidden" if hide_show_filter.value else "visible"
                        self.stdout.write(f"  - {field_name}: {status} (existing)")
        
        self.stdout.write(
            self.style.SUCCESS('Successfully configured SEAMS model display settings!')
        )
