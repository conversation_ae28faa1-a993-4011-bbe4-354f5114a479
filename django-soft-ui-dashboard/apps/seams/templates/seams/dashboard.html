{% extends "layouts/base.html" %}

{% block title %} SEAMS Dashboard {% endblock title %}

{% block breadcrumbs %}{% endblock breadcrumbs %}

{% block content %}

<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header pb-0">
          <h6>SEAMS - Security and Asset Management System</h6>
          <p class="text-sm mb-0">
            Comprehensive asset management dashboard for hardware, software, certificates, and IP addresses.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row">
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">Hardware</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ hardware_count }}
                  <span class="text-success text-sm font-weight-bolder">items</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                <i class="ni ni-laptop text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">Software</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ software_count }}
                  <span class="text-info text-sm font-weight-bolder">licenses</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                <i class="ni ni-app text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">Certificates</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ certificate_count }}
                  <span class="text-warning text-sm font-weight-bolder">certs</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                <i class="ni ni-lock-circle-open text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">IP Addresses</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ ip_count }}
                  <span class="text-success text-sm font-weight-bolder">IPs</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                <i class="ni ni-world text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">Users</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ user_count }}
                  <span class="text-primary text-sm font-weight-bolder">users</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                <i class="ni ni-single-02 text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-8">
              <div class="numbers">
                <p class="text-sm mb-0 text-capitalize font-weight-bold">Roles</p>
                <h5 class="font-weight-bolder mb-0">
                  {{ role_count }}
                  <span class="text-secondary text-sm font-weight-bolder">roles</span>
                </h5>
              </div>
            </div>
            <div class="col-4 text-end">
              <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md">
                <i class="ni ni-badge text-lg opacity-10" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Access Links -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header pb-0">
          <h6>Quick Access</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/hardware/" class="btn btn-outline-primary w-100">
                <i class="ni ni-laptop me-2"></i>Hardware
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/software/" class="btn btn-outline-info w-100">
                <i class="ni ni-app me-2"></i>Software
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/certificates/" class="btn btn-outline-warning w-100">
                <i class="ni ni-lock-circle-open me-2"></i>Certificates
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/ipaddress/" class="btn btn-outline-success w-100">
                <i class="ni ni-world me-2"></i>IP Addresses
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/users/" class="btn btn-outline-primary w-100">
                <i class="ni ni-single-02 me-2"></i>Users
              </a>
            </div>
            <div class="col-md-2 mb-3">
              <a href="/dynamic-dt/roles/" class="btn btn-outline-secondary w-100">
                <i class="ni ni-badge me-2"></i>Roles
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Items -->
  <div class="row mt-4">
    <div class="col-lg-4 mb-4">
      <div class="card h-100">
        <div class="card-header pb-0">
          <h6>Recent Hardware</h6>
        </div>
        <div class="card-body">
          {% for hw in recent_hardware %}
          <div class="d-flex align-items-center mb-3">
            <div class="icon icon-sm icon-shape bg-gradient-primary shadow text-center border-radius-md me-3">
              <i class="ni ni-laptop text-white opacity-10"></i>
            </div>
            <div>
              <h6 class="mb-0 text-sm">{{ hw.model }}</h6>
              <p class="text-xs text-muted mb-0">{{ hw.serial|default:"No Serial" }} - {{ hw.location|default:"No Location" }}</p>
            </div>
          </div>
          {% empty %}
          <p class="text-muted">No hardware items found.</p>
          {% endfor %}
        </div>
      </div>
    </div>
    
    <div class="col-lg-4 mb-4">
      <div class="card h-100">
        <div class="card-header pb-0">
          <h6>Recent Software</h6>
        </div>
        <div class="card-body">
          {% for sw in recent_software %}
          <div class="d-flex align-items-center mb-3">
            <div class="icon icon-sm icon-shape bg-gradient-info shadow text-center border-radius-md me-3">
              <i class="ni ni-app text-white opacity-10"></i>
            </div>
            <div>
              <h6 class="mb-0 text-sm">{{ sw.name }}</h6>
              <p class="text-xs text-muted mb-0">{{ sw.serial|default:"No Serial" }} - Expires: {{ sw.expiry_date|default:"No Expiry" }}</p>
            </div>
          </div>
          {% empty %}
          <p class="text-muted">No software items found.</p>
          {% endfor %}
        </div>
      </div>
    </div>
    
    <div class="col-lg-4 mb-4">
      <div class="card h-100">
        <div class="card-header pb-0">
          <h6>Recent Certificates</h6>
        </div>
        <div class="card-body">
          {% for cert in recent_certificates %}
          <div class="d-flex align-items-center mb-3">
            <div class="icon icon-sm icon-shape bg-gradient-warning shadow text-center border-radius-md me-3">
              <i class="ni ni-lock-circle-open text-white opacity-10"></i>
            </div>
            <div>
              <h6 class="mb-0 text-sm">{{ cert.certificate_type }}</h6>
              <p class="text-xs text-muted mb-0">{{ cert.serial|default:"No Serial" }} - Expires: {{ cert.expiry_date|default:"No Expiry" }}</p>
            </div>
          </div>
          {% empty %}
          <p class="text-muted">No certificates found.</p>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock content %}
