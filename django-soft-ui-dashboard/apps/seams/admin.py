from django.contrib import admin
from .models import Role, Hardware, Software, Certificate, IPAddress, User

# Register your models here.

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('rid', 'role')
    search_fields = ('role',)
    ordering = ('role',)


@admin.register(Hardware)
class HardwareAdmin(admin.ModelAdmin):
    list_display = ('hid', 'model', 'serial', 'location', 'user_assigned', 'install_date')
    list_filter = ('model', 'location', 'install_date')
    search_fields = ('model', 'serial', 'location', 'user_assigned')
    ordering = ('model', 'serial')


@admin.register(Software)
class SoftwareAdmin(admin.ModelAdmin):
    list_display = ('sid', 'name', 'serial', 'user_assigned', 'hardware_assigned', 'install_date', 'expiry_date')
    list_filter = ('name', 'hardware_assigned', 'install_date', 'expiry_date')
    search_fields = ('name', 'serial', 'user_assigned')
    ordering = ('name',)


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ('cid', 'certificate_type', 'serial', 'hardware_assigned', 'install_date', 'expiry_date')
    list_filter = ('certificate_type', 'hardware_assigned', 'install_date', 'expiry_date')
    search_fields = ('certificate_type', 'serial')
    ordering = ('certificate_type', 'expiry_date')


@admin.register(IPAddress)
class IPAddressAdmin(admin.ModelAdmin):
    list_display = ('ipid', 'ip_type', 'address', 'hardware_assigned', 'install_date', 'expiry_date')
    list_filter = ('ip_type', 'hardware_assigned', 'install_date', 'expiry_date')
    search_fields = ('ip_type', 'address')
    ordering = ('ip_type', 'address')


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('uid', 'first_name', 'last_name', 'email_address', 'role', 'department', 'mfa_status')
    list_filter = ('role', 'department', 'mfa_status')
    search_fields = ('first_name', 'last_name', 'email_address', 'department')
    ordering = ('last_name', 'first_name')
