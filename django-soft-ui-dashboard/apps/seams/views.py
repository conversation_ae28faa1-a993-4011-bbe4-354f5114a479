from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from .models import Hardware, Software, Certificate, IPAddress

# Create your views here.

# Helper functions removed

def seams_dashboard(request):
    """
    SEAMS Dashboard view showing overview of all assets
    """
    context = {
        # Asset counts (focused on operational assets)
        'hardware_count': Hardware.objects.count(),
        'software_count': Software.objects.count(),
        'certificate_count': Certificate.objects.count(),
        'ip_count': IPAddress.objects.count(),

        'segment': 'seams_dashboard'
    }

    return render(request, 'seams/dashboard.html', context)


def seams_reports(request):
    """
    SEAMS Reports view
    """
    # Hardware by location
    hardware_by_location = {}
    for hw in Hardware.objects.all():
        location = hw.location or 'Unknown'
        if location not in hardware_by_location:
            hardware_by_location[location] = 0
        hardware_by_location[location] += 1

    context = {
        'hardware_by_location': hardware_by_location,
        'segment': 'seams_reports'
    }

    return render(request, 'seams/reports.html', context)
