# Generated by Django 4.2.9 on 2025-06-01 01:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('cid', models.AutoField(db_column='CID', primary_key=True, serialize=False)),
                ('certificate_type', models.Char<PERSON>ield(db_column='CertificateType', max_length=255)),
                ('serial', models.CharField(blank=True, db_column='Serial', max_length=255, null=True)),
                ('install_date', models.CharField(blank=True, db_column='InstallDate', max_length=50, null=True)),
                ('expiry_date', models.CharField(blank=True, db_column='ExpiryDate', max_length=50, null=True)),
            ],
            options={
                'db_table': 'Certificates',
            },
        ),
        migrations.CreateModel(
            name='Hardware',
            fields=[
                ('hid', models.AutoField(db_column='HID', primary_key=True, serialize=False)),
                ('model', models.CharField(db_column='Model', max_length=255)),
                ('serial', models.CharField(blank=True, db_column='Serial', max_length=255, null=True)),
                ('location', models.CharField(blank=True, db_column='Location', max_length=255, null=True)),
                ('user_assigned', models.CharField(blank=True, db_column='UserAssigned', max_length=255, null=True)),
                ('install_date', models.CharField(blank=True, db_column='InstallDate', max_length=50, null=True)),
            ],
            options={
                'db_table': 'Hardware',
            },
        ),
        migrations.CreateModel(
            name='IPAddress',
            fields=[
                ('ipid', models.AutoField(db_column='IPID', primary_key=True, serialize=False)),
                ('ip_type', models.CharField(db_column='IPType', max_length=50)),
                ('address', models.CharField(blank=True, db_column='Address', max_length=45, null=True)),
                ('install_date', models.CharField(blank=True, db_column='InstallDate', max_length=50, null=True)),
                ('expiry_date', models.CharField(blank=True, db_column='ExpiryDate', max_length=50, null=True)),
            ],
            options={
                'db_table': 'IPAddress',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('rid', models.AutoField(db_column='RID', primary_key=True, serialize=False)),
                ('role', models.CharField(db_column='Role', max_length=100)),
            ],
            options={
                'db_table': 'Roles',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('uid', models.AutoField(db_column='UID', primary_key=True, serialize=False)),
                ('last_name', models.CharField(db_column='LastName', max_length=100)),
                ('first_name', models.CharField(db_column='FirstName', max_length=100)),
                ('email_address', models.EmailField(db_column='EmailAddress', max_length=255)),
                ('mfa_status', models.BooleanField(db_column='MFAStatus', default=False)),
                ('department', models.CharField(blank=True, db_column='Department', max_length=100, null=True)),
                ('role', models.ForeignKey(db_column='RoleID', on_delete=django.db.models.deletion.PROTECT, to='seams.role')),
            ],
            options={
                'db_table': 'User',
            },
        ),
        migrations.CreateModel(
            name='Software',
            fields=[
                ('sid', models.AutoField(db_column='SID', primary_key=True, serialize=False)),
                ('name', models.CharField(db_column='Name', max_length=255)),
                ('serial', models.CharField(blank=True, db_column='Serial', max_length=255, null=True)),
                ('user_assigned', models.CharField(blank=True, db_column='UserAssigned', max_length=255, null=True)),
                ('install_date', models.CharField(blank=True, db_column='InstallDate', max_length=50, null=True)),
                ('expiry_date', models.CharField(blank=True, db_column='ExpiryDate', max_length=50, null=True)),
                ('hardware_assigned', models.ForeignKey(blank=True, db_column='HardwareAssigned', null=True, on_delete=django.db.models.deletion.SET_NULL, to='seams.hardware')),
            ],
            options={
                'db_table': 'Software',
            },
        ),
        migrations.AddIndex(
            model_name='role',
            index=models.Index(fields=['role'], name='idx_Roles_Role'),
        ),
        migrations.AddField(
            model_name='ipaddress',
            name='hardware_assigned',
            field=models.ForeignKey(blank=True, db_column='HardwareAssigned', null=True, on_delete=django.db.models.deletion.SET_NULL, to='seams.hardware'),
        ),
        migrations.AddIndex(
            model_name='hardware',
            index=models.Index(fields=['model'], name='idx_hw_model'),
        ),
        migrations.AddField(
            model_name='certificate',
            name='hardware_assigned',
            field=models.ForeignKey(blank=True, db_column='HardwareAssigned', null=True, on_delete=django.db.models.deletion.SET_NULL, to='seams.hardware'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['last_name'], name='idx_user_lname'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['first_name'], name='idx_user_fname'),
        ),
        migrations.AddIndex(
            model_name='software',
            index=models.Index(fields=['name'], name='idx_sw_name'),
        ),
        migrations.AddIndex(
            model_name='ipaddress',
            index=models.Index(fields=['ip_type'], name='idx_ip_type'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['certificate_type'], name='idx_cert_type'),
        ),
    ]
