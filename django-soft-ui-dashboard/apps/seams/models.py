from django.db import models
from django.core.validators import EmailValidator

# Create your models here.

class Role(models.Model):
    """
    Roles table for user role management
    """
    rid = models.AutoField(primary_key=True, db_column='RID')
    role = models.CharField(max_length=100, null=False, db_column='Role')

    class Meta:
        db_table = 'Roles'
        indexes = [
            models.Index(fields=['role'], name='idx_Roles_Role'),
        ]

    def __str__(self):
        return self.role


class Hardware(models.Model):
    """
    Hardware assets management table
    """
    hid = models.AutoField(primary_key=True, db_column='HID')
    model = models.CharField(max_length=255, null=False, db_column='Model')
    serial = models.CharField(max_length=255, null=True, blank=True, db_column='Serial')
    location = models.CharField(max_length=255, null=True, blank=True, db_column='Location')
    user_assigned = models.CharField(max_length=255, null=True, blank=True, db_column='UserAssigned')
    install_date = models.CharField(max_length=50, null=True, blank=True, db_column='InstallDate')

    class Meta:
        db_table = 'Hardware'
        indexes = [
            models.Index(fields=['model'], name='idx_hw_model'),
        ]

    def __str__(self):
        return f"{self.model} ({self.serial or 'No Serial'})"


class Software(models.Model):
    """
    Software assets management table
    """
    sid = models.AutoField(primary_key=True, db_column='SID')
    name = models.CharField(max_length=255, null=False, db_column='Name')
    serial = models.CharField(max_length=255, null=True, blank=True, db_column='Serial')
    user_assigned = models.CharField(max_length=255, null=True, blank=True, db_column='UserAssigned')
    hardware_assigned = models.ForeignKey(
        Hardware,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_column='HardwareAssigned',
        to_field='hid'
    )
    install_date = models.CharField(max_length=50, null=True, blank=True, db_column='InstallDate')
    expiry_date = models.CharField(max_length=50, null=True, blank=True, db_column='ExpiryDate')

    class Meta:
        db_table = 'Software'
        indexes = [
            models.Index(fields=['name'], name='idx_sw_name'),
        ]

    def __str__(self):
        return self.name


class Certificate(models.Model):
    """
    Digital certificates management table
    """
    cid = models.AutoField(primary_key=True, db_column='CID')
    certificate_type = models.CharField(max_length=255, null=False, db_column='CertificateType')
    serial = models.CharField(max_length=255, null=True, blank=True, db_column='Serial')
    hardware_assigned = models.ForeignKey(
        Hardware,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_column='HardwareAssigned',
        to_field='hid'
    )
    install_date = models.CharField(max_length=50, null=True, blank=True, db_column='InstallDate')
    expiry_date = models.CharField(max_length=50, null=True, blank=True, db_column='ExpiryDate')

    class Meta:
        db_table = 'Certificates'
        indexes = [
            models.Index(fields=['certificate_type'], name='idx_cert_type'),
        ]

    def __str__(self):
        return f"{self.certificate_type} ({self.serial or 'No Serial'})"


class IPAddress(models.Model):
    """
    IP Address management table
    """
    ipid = models.AutoField(primary_key=True, db_column='IPID')
    ip_type = models.CharField(max_length=50, null=False, db_column='IPType')
    address = models.CharField(max_length=45, null=True, blank=True, db_column='Address')  # IPv6 can be up to 45 chars
    hardware_assigned = models.ForeignKey(
        Hardware,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_column='HardwareAssigned',
        to_field='hid'
    )
    install_date = models.CharField(max_length=50, null=True, blank=True, db_column='InstallDate')
    expiry_date = models.CharField(max_length=50, null=True, blank=True, db_column='ExpiryDate')

    class Meta:
        db_table = 'IPAddress'
        indexes = [
            models.Index(fields=['ip_type'], name='idx_ip_type'),
        ]

    def __str__(self):
        return f"{self.ip_type}: {self.address or 'No Address'}"


class User(models.Model):
    """
    SEAMS User management table (separate from Django's built-in User model)
    """
    uid = models.AutoField(primary_key=True, db_column='UID')
    last_name = models.CharField(max_length=100, null=False, db_column='LastName')
    first_name = models.CharField(max_length=100, null=False, db_column='FirstName')
    email_address = models.EmailField(max_length=255, null=False, db_column='EmailAddress')
    mfa_status = models.BooleanField(null=False, default=False, db_column='MFAStatus')
    role = models.ForeignKey(
        Role,
        on_delete=models.PROTECT,
        null=False,
        db_column='RoleID',
        to_field='rid'
    )
    department = models.CharField(max_length=100, null=True, blank=True, db_column='Department')

    class Meta:
        db_table = 'User'
        indexes = [
            models.Index(fields=['last_name'], name='idx_user_lname'),
            models.Index(fields=['first_name'], name='idx_user_fname'),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email_address})"
